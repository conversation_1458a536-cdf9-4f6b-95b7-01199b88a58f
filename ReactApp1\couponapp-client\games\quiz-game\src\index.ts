import MainGame from './game/GameMain'
import { ConfigKeyEditor } from './editor/ConfigKeyEditor'
import MainConfigEditor from './editor/MainConfigEditor'
import { PreviewScene } from './game/Scenes'
import { defaultGameConfig, ReactGameConfig } from './types/config'
import { GameScreenId } from './types/screen'
import { GameModule, PreviewScreenDefinition } from '@repo/shared/lib/game/game'
import { registerValueSources } from '@repo/shared/lib/dynamic-values/registry'

// Preview screen definitions for the game
const previewScreens: PreviewScreenDefinition[] = [
    {
        screenId: 'start' as GameScreenId,
        displayName: 'Start Screen',
        visibleCheck: (config) => config.gameStartHandler?.enableStartScreen === true,
    },
    {
        screenId: 'main' as GameScreenId,
        displayName: 'Main Game Screen',
    },
    {
        screenId: 'game-over-win' as GameScreenId,
        displayName: 'Game Over - Win',
        visibleCheck: (config) => config.gameRewardsHandler?.rewardsEnabled === true,
    },
    {
        screenId: 'game-over-lost' as GameScreenId,
        displayName: 'Game Over - Lost',
        visibleCheck: () => true, // Always show game over screen since it's used for all end scenarios
    },
]

const widgetSlots = [
    {
        widgetId: 'gameOverLost',
        displayName: 'Game Over - Lost',
    },
    {
        widgetId: 'gameOverWin',
        displayName: 'Game Over - Win',
    },
]

const QuizGame: GameModule = {
    id: 'quiz-game',
    name: 'Quiz Game',
    runtimeComponent: MainGame,
    editorComponent: MainConfigEditor,
    configKeyEditor: ConfigKeyEditor,
    defaultConfig: defaultGameConfig,
    previewScene: PreviewScene,
    previewScreens: previewScreens,
    configType: ReactGameConfig,
    widgetSlots: widgetSlots
}

export default QuizGame

export { MainGame, MainConfigEditor }


registerValueSources('quiz-game', [
  {
    path: "game/{widgetId}/score",
    type: "number",
    label: "Player Score",
    description: "Current player's game score"
  },
  {
    path: "game/{widgetId}/currentQuestion",
    type: "number",
    label: "Current question",
    description: "The current question that is being displayed in game"
  }
])