# Quiz Game - Screen Flow Requirements

## Screen Structure
The quiz game has 4 possible screens:
1. **start** (optional) - Initial game screen
2. **main** - Main gameplay screen
3. **game-over-win** - Victory screen
4. **game-over-lost** - Defeat screen

## Game Configuration Variables
- `enableStartScreen` - Shows/hides start screen
- `rewardsEnabled` - Enables reward system
- `useLives` - Enables lives system
- `livesCount` - Number of lives (default: 3)

## Screen Flow Logic

### 1. Game Start Flow
```
Game Launch → Check enableStartScreen
├── TRUE → Show 'start' screen → User clicks start → 'main' screen
└── FALSE → Show 'main' screen directly
```

### 2. Main Game Events & Transitions

#### A. Correct Answer
- Add score
- Continue to next question
- Stay on 'main' screen

#### B. Wrong Answer (Lives System OFF)
- Show answer feedback
- Continue to next question
- Stay on 'main' screen

#### C. Wrong Answer (Lives System ON)
- Show answer feedback
- Lose 1 life
- Check remaining lives:
  ```
  Lives > 0 → Continue playing on 'main' screen
  Lives = 0 → Transition to end game logic
  ```

#### D. Time Expired (if timer enabled)
- Treat as wrong answer
- Follow wrong answer logic above

#### E. Quiz Completion (All Questions Answered)
- Transition to end game logic

### 3. End Game Logic Decision Tree

```
Game End Trigger → Check Rewards System
├── Rewards ENABLED
│   └── Check if player won reward
│       ├── Won Reward → 'game-over-win' screen
│       └── No Reward → 'game-over-lost' screen
│
└── Rewards DISABLED
    └── Always → 'game-over-lost' screen
```

### 4. Detailed Flow Scenarios

#### Scenario A: Rewards ON + Lives ON
```
Wrong Answer → Lose Life → Check Lives
├── Lives > 0 → Continue playing
└── Lives = 0 → Check reward roll
    ├── Won → 'game-over-win'
    └── Lost → 'game-over-lost'

Quiz Complete → Check reward roll
├── Won → 'game-over-win'
└── Lost → 'game-over-lost'
```

#### Scenario B: Rewards ON + Lives OFF
```
Wrong Answer → Show feedback → Continue playing
Quiz Complete → Check reward roll
├── Won → 'game-over-win'
└── Lost → 'game-over-lost'
```

#### Scenario C: Rewards OFF + Lives ON
```
Wrong Answer → Lose Life → Check Lives
├── Lives > 0 → Continue playing
└── Lives = 0 → 'game-over-lost'

Quiz Complete → 'game-over-lost'
```

#### Scenario D: Rewards OFF + Lives OFF
```
Wrong Answer → Show feedback → Continue playing
Quiz Complete → 'game-over-lost'
```

### 5. Screen Transition Rules

#### To 'game-over-win':
- Rewards are enabled AND
- Player won a reward (based on pre-rolled result)

#### To 'game-over-lost':
- Player ran out of lives OR
- Quiz completed without winning reward OR
- Rewards disabled (always lose scenario)

### 6. Widget Slots Usage

#### gameOverWin Widget Slot:
- Used on 'game-over-win' screen
- Should contain reward display elements
- Victory messaging and celebration

#### gameOverLost Widget Slot:
- Used on 'game-over-lost' screen
- Should contain consolation messaging
- Game over feedback

### 7. Event Emissions

#### GameFinished Event:
Emitted when transitioning to any end screen:
```javascript
gameEvents.emit('GameFinished', {
    score: bestScore,
    widgetId: widgetId,
});
```

### 8. Key Implementation Notes

- No try-again screens - direct win/loss outcomes
- Answer feedback shown inline during gameplay
- Lives decremented immediately on wrong answers
- Reward rolls happen at round start, checked at game end
- Screen transitions may have delays for user experience (1.5s for wrong answers)
- Preview mode bypasses all game logic and event emissions

### 9. Configuration Dependencies

#### Start Screen:
- Requires: `gameStartHandler.enableStartScreen = true`
- Widget: Uses start screen button configuration

#### Lives System:
- Requires: `gameEndHandler.useLives = true`
- Config: `gameEndHandler.livesCount` (number)

#### Rewards System:
- Requires: `gameRewardsHandler.rewardsEnabled = true`
- Affects: End screen determination only

#### Timer System:
- Optional: Can be enabled independently
- Behavior: Time expiry = wrong answer