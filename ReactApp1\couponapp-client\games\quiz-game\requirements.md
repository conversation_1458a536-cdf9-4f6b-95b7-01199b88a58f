# Quiz Game Flow Requirements

## Game Flow Cases

### Lives ON + Rewards ON
Wrong answer → Check reward → If won: Reward Screen → End | If lost: Lose life → If lives=0: Game Over → End

### Lives ON + Rewards OFF
Wrong answer → Lose life → If lives=0: Game Over → End

### Lives OFF + Rewards ON
Wrong answer → Check reward → If won: Reward Screen → End | If lost: Continue

### Lives OFF + Rewards OFF
Wrong answer → Continue

## Additional Cases

### Timer Timeout
Timer expires → Treated as wrong answer → Follow wrong answer flow based on lives/rewards config

### Quiz Completion
All questions answered correctly → Game ends (no reward check)

### Game Start
Start screen enabled → Start Screen → Main | Start screen disabled → Main directly

### After Reward/Game Over
Reward claimed → Game ends | Game over → Game ends | Try again → New round → Main

## Screens
- **Start**: Optional intro
- **Main**: Quiz gameplay
- **Reward**: Show won reward
- **Out of Lives**: Game over when lives=0
